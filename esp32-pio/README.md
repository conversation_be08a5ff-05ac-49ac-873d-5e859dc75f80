# ESP32 WiFi 天气API项目

## 项目概述

这个项目实现了ESP32连接WiFi并请求心知天气API的功能。当WiFi连接失败时，会自动启动一个配置门户，让您通过Web界面输入正确的WiFi密码。

## 功能特性

1. **WiFi连接管理**
   - 自动扫描可用WiFi网络
   - 智能连接重试机制
   - 详细的连接状态显示

2. **配置门户**
   - 当WiFi连接失败时自动启动
   - 提供友好的Web界面配置WiFi
   - 支持开放网络和加密网络

3. **天气API请求**
   - 连接成功后自动请求心知天气API
   - 获取北京当前天气信息
   - 解析并显示JSON响应数据

4. **实时监控**
   - 持续监控WiFi连接状态
   - 连接断开时自动重连
   - 详细的调试信息输出

## 使用方法

### 第一次使用或WiFi连接失败时：

1. **连接配置网络**
   - 在您的手机或电脑上搜索WiFi网络
   - 连接到名为 `ESP32-Config` 的网络
   - 密码：`12345678`

2. **打开配置页面**
   - 打开浏览器访问：`http://***********`
   - 或者直接访问：`***********`

3. **配置WiFi信息**
   - 在表单中输入您的WiFi网络名称（SSID）
   - 输入WiFi密码
   - 如果是开放网络，密码留空
   - 点击"保存并连接"

4. **等待连接**
   - ESP32会尝试使用新配置连接WiFi
   - 连接成功后会自动请求天气API
   - 如果连接失败，配置门户会重新启动

### WiFi连接成功后：

- ESP32会每30秒检查一次连接状态
- 显示当前IP地址和信号强度
- 如果连接断开，会自动尝试重连

## API信息

- **API提供商**：心知天气 (Seniverse)
- **API密钥**：SZf6L_nL3t4vSLKPQ
- **请求城市**：北京
- **返回格式**：JSON
- **更新频率**：连接成功时请求一次

## 串口监视器输出示例

```
ESP32 WiFi and API Test Starting...
====================================

开始扫描WiFi网络...
扫描完成，发现 5 个网络:
1: ChinaNet-6MYM (-61 dBm) 加密
2: 老穆 (-68 dBm) 加密  
3: tatahome (-82 dBm) 加密 ← 目标网络!
4: ChinaNet-xvcj (-91 dBm) 加密
5: CMCC-501 (-92 dBm) 加密
✓ 找到目标网络 'tatahome'

尝试连接到WiFi网络: tatahome
使用密码: m1234567
正在连接.....
✓ WiFi连接成功！
IP地址: *************
信号强度: -82 dBm

开始请求天气API...
=== API响应JSON数据 ===
{"results":[{"location":{"id":"WX4FBXXFKE4F","name":"北京","country":"CN","path":"北京,北京,中国","timezone":"Asia/Shanghai","timezone_offset":"+08:00"},"now":{"text":"晴","code":"0","temperature":"15"},"last_update":"2024-01-15T14:30:00+08:00"}]}

=== 解析后的天气信息 ===
城市: 北京
国家: CN
当前天气: 晴
温度: 15°C
最后更新: 2024-01-15T14:30:00+08:00
```

## 故障排除

### 如果配置门户无法访问：
1. 确认已连接到 `ESP32-Config` 网络
2. 尝试访问 `***********`
3. 检查手机是否自动切换到其他网络

### 如果WiFi连接一直失败：
1. 检查WiFi密码是否正确
2. 确认WiFi网络在ESP32信号范围内
3. 检查路由器是否有MAC地址过滤
4. 尝试重启路由器

### 如果API请求失败：
1. 检查网络连接是否正常
2. 确认API密钥是否有效
3. 检查防火墙设置

## 技术规格

- **开发板**：ESP32
- **开发环境**：PlatformIO
- **主要库**：
  - WiFi (ESP32内置)
  - HTTPClient (ESP32内置)
  - WebServer (ESP32内置)
  - DNSServer (ESP32内置)
  - ArduinoJson 6.21.5

## 作者

由 Augment Agent 开发，基于用户需求实现ESP32 WiFi连接和天气API请求功能。

---

**注意**：请确保您的WiFi网络密码正确，并且ESP32在网络信号覆盖范围内。如有问题，请查看串口监视器的详细输出信息。
